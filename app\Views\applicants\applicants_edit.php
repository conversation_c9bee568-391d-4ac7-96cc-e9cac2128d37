<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>
                        <i class="fas fa-user-edit mr-2"></i>
                        Edit Applicant
                    </h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Home</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('positions') ?>">Positions</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('positions/groups') ?>">Position Groups</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('applicants/list/' . $position['id']) ?>">Applicants</a></li>
                        <li class="breadcrumb-item active">Edit Applicant</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <section class="content">
        <div class="container-fluid">
            <?= form_open('applicants/update/' . $applicant['id']) ?>
            <div class="row">
                <div class="col-md-6">
                    <div class="card card-primary card-outline">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-user mr-2"></i>
                                Personal Information
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="form-group">
                                <label for="name">Name</label>
                                <input type="text" class="form-control" id="name" name="name" value="<?= $applicant['name'] ?>" required>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="sex">Sex</label>
                                        <select class="form-control" id="sex" name="sex" required>
                                            <option value="Male" <?= $applicant['sex'] == 'Male' ? 'selected' : '' ?>>Male</option>
                                            <option value="Female" <?= $applicant['sex'] == 'Female' ? 'selected' : '' ?>>Female</option>
                                            <option value="Other" <?= $applicant['sex'] == 'Other' ? 'selected' : '' ?>>Other</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="age">Age</label>
                                        <input type="number" class="form-control" id="age" name="age" value="<?= $applicant['age'] ?>" required>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="place_origin">Place of Origin</label>
                                <input type="text" class="form-control" id="place_origin" name="place_origin" value="<?= $applicant['place_origin'] ?>">
                            </div>
                            <div class="form-group">
                                <label for="address_location">Address/Location</label>
                                <input type="text" class="form-control" id="address_location" name="address_location" value="<?= $applicant['address_location'] ?>">
                            </div>
                            <div class="form-group">
                                <label for="contact_details">Contact Details <small>(e.g. Email, Phone Number)</small> </label>
                                <input type="text" class="form-control" id="contact_details" name="contact_details" value="<?= $applicant['contact_details'] ?>">
                            </div>
                            <div class="form-group">
                                <label for="nid_number">NID Number</label>
                                <input type="text" class="form-control" id="nid_number" name="nid_number" value="<?= $applicant['nid_number'] ?>">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card card-primary card-outline">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-briefcase mr-2"></i>
                                Professional Information
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="form-group">
                                <label for="current_employer">Current Employer</label>
                                <input type="text" class="form-control" id="current_employer" name="current_employer" value="<?= $applicant['current_employer'] ?>">
                            </div>
                            <div class="form-group">
                                <label for="current_position">Current Position</label>
                                <input type="text" class="form-control" id="current_position" name="current_position" value="<?= $applicant['current_position'] ?>">
                            </div>
                            <div class="form-group">
                                <label for="qualification_text"><strong>Qualifications:</strong></label>
                                <textarea class="form-control" id="qualification_text" name="qualification_text" rows="3"><?= set_value('qualification_text', $applicant['qualification_text']) ?></textarea>
                            </div>
                            <div class="form-group">
                                <label for="other_trainings"><strong>Other Trainings:</strong></label>
                                <textarea class="form-control" id="other_trainings" name="other_trainings" rows="3"><?= set_value('other_trainings', $applicant['other_trainings']) ?></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="card card-primary card-outline">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-tools mr-2"></i>
                                Skills and Experience
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="form-group">
                                <label for="knowledge"><strong>Knowledge:</strong></label>
                                <textarea class="form-control" id="knowledge" name="knowledge" rows="3"><?= set_value('knowledge', $applicant['knowledge']) ?></textarea>
                            </div>
                            <div class="form-group">
                                <label for="skills_competencies"><strong>Skills and Competencies:</strong></label>
                                <textarea class="form-control" id="skills_competencies" name="skills_competencies" rows="3"><?= set_value('skills_competencies', $applicant['skills_competencies']) ?></textarea>
                            </div>
                            <div class="form-group">
                                <label for="job_experiences"><strong>Job Experiences:</strong></label>
                                <textarea class="form-control" id="job_experiences" name="job_experiences" rows="3"><?= set_value('job_experiences', $applicant['job_experiences']) ?></textarea>
                            </div>
                            <div class="form-group">
                                <label for="publications"><strong>Publications:</strong></label>
                                <textarea class="form-control" id="publications" name="publications" rows="3"><?= set_value('publications', $applicant['publications']) ?></textarea>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card card-primary card-outline">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-info-circle mr-2"></i>
                                Additional Information
                            </h3>
                        </div>
                        <div class="card-body">

                            <div class="form-group">
                                <label for="awards"><strong>Awards:</strong></label>
                                <textarea class="form-control" id="awards" name="awards" rows="3"><?= set_value('awards', $applicant['awards']) ?></textarea>
                            </div>
                            <div class="form-group">
                                <label for="referees"><strong>Referees:</strong></label>
                                <textarea class="form-control" id="referees" name="referees" rows="3"><?= set_value('referees', $applicant['referees']) ?></textarea>
                            </div>
                            <div class="form-group">
                                <label for="comments"><strong>Comments:</strong></label>
                                <textarea class="form-control" id="comments" name="comments" rows="3"><?= set_value('comments', $applicant['comments']) ?></textarea>
                            </div>

                            <div class="form-group">
                                <label for="is_active">Active</label>
                                <select class="form-control" id="is_active" name="is_active">
                                    <option value="1" <?= $applicant['is_active'] ? 'selected' : '' ?>>Yes</option>
                                    <option value="0" <?= !$applicant['is_active'] ? 'selected' : '' ?>>No</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row mb-4">
                <div class="col-12">
                    <div class="d-flex justify-content-between">
                        <a href="<?= base_url('applicants/list/' . $position['id']) ?>" class="btn btn-secondary">
                            <i class="fas fa-arrow-left mr-2"></i> Back to List
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save mr-2"></i> Update Applicant
                        </button>
                    </div>
                </div>
            </div>
            <?= form_close() ?>
        </div>
    </section>
</div>
<?= $this->endSection() ?>
