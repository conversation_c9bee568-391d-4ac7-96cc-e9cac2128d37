<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>
                        <i class="fas fa-tasks mr-2"></i>
                        <?= $title ?>
                    </h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Home</a></li>
                        <li class="breadcrumb-item active">Exercises</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <section class="content">
        <div class="container-fluid">
            <!-- Flash Messages -->
            <?php if (session()->getFlashdata('success')): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle mr-2"></i>
                    <?= session()->getFlashdata('success') ?>
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
            <?php endif; ?>

            <?php if (session()->getFlashdata('error')): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle mr-2"></i>
                    <?= session()->getFlashdata('error') ?>
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
            <?php endif; ?>

            <div class="row">
                <div class="col-12">
                    <div class="card card-primary card-outline">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-tasks mr-2"></i>
                                Exercise Management
                            </h3>
                            <div class="card-tools">
                                <a href="<?= base_url('exercise/create') ?>" class="btn btn-primary btn-sm">
                                    <i class="fas fa-plus mr-1"></i>
                                    Create New Exercise
                                </a>
                            </div>
                        </div>
                        <div class="card-body">
                            <?php if (empty($exercises)): ?>
                                <div class="text-center py-5">
                                    <i class="fas fa-tasks fa-3x text-muted mb-3"></i>
                                    <h4 class="text-muted">No Exercises Found</h4>
                                    <p class="text-muted">Create your first exercise to start managing recruitment rounds.</p>
                                    <a href="<?= base_url('exercise/create') ?>" class="btn btn-primary">
                                        <i class="fas fa-plus mr-1"></i>
                                        Create New Exercise
                                    </a>
                                </div>
                            <?php else: ?>
                                <?php foreach ($exercises as $exercise): ?>
                                    <div class="info-box mb-3">
                                        <span class="info-box-icon <?= $exercise['status'] === 'active' ? 'bg-success' : ($exercise['status'] === 'completed' ? 'bg-info' : ($exercise['status'] === 'archived' ? 'bg-secondary' : 'bg-warning')) ?>">
                                            <i class="fas fa-tasks"></i>
                                        </span>
                                        <div class="info-box-content">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <div>
                                                    <span class="info-box-text">
                                                        <strong><?= esc($exercise['exercise_title']) ?></strong>
                                                        <span class="badge badge-<?= $exercise['status'] === 'active' ? 'success' : ($exercise['status'] === 'completed' ? 'info' : ($exercise['status'] === 'archived' ? 'secondary' : 'warning')) ?> ml-2">
                                                            <?= ucfirst($exercise['status']) ?>
                                                        </span>
                                                    </span>
                                                    <span class="info-box-number">
                                                        <?= esc($exercise['description'] ?: 'No description provided') ?>
                                                    </span>
                                                    <div class="progress-description">
                                                        <small class="text-muted">
                                                            <i class="fas fa-users mr-1"></i><?= $exercise['stats']['applicants_count'] ?> Applicants
                                                            <i class="fas fa-briefcase ml-3 mr-1"></i><?= $exercise['stats']['positions_count'] ?> Positions
                                                            <i class="fas fa-user-tie ml-3 mr-1"></i><?= $exercise['stats']['interviewers_count'] ?> Interviewers
                                                            <i class="fas fa-question-circle ml-3 mr-1"></i><?= $exercise['stats']['questions_count'] ?> Questions
                                                        </small>
                                                        <br>
                                                        <small class="text-muted">
                                                            <i class="fas fa-calendar mr-1"></i>
                                                            Created: <?= date('M d, Y', strtotime($exercise['created_at'])) ?>
                                                        </small>
                                                    </div>
                                                </div>
                                                <div class="btn-group" role="group">
                                                    <a href="<?= base_url('exercise/select/' . $exercise['id']) ?>"
                                                       class="btn btn-primary btn-sm">
                                                        <i class="fas fa-play mr-1"></i>
                                                        Select
                                                    </a>
                                                    <a href="<?= base_url('exercise/show/' . $exercise['id']) ?>"
                                                       class="btn btn-info btn-sm">
                                                        <i class="fas fa-eye mr-1"></i>
                                                        View
                                                    </a>
                                                    <div class="btn-group" role="group">
                                                        <button type="button" class="btn btn-secondary btn-sm dropdown-toggle"
                                                                data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                            <i class="fas fa-cog"></i>
                                                        </button>
                                                        <div class="dropdown-menu">
                                                            <a class="dropdown-item" href="<?= base_url('exercise/edit/' . $exercise['id']) ?>">
                                                                <i class="fas fa-edit mr-2"></i>Edit
                                                            </a>
                                                            <?php if ($exercise['status'] === 'draft'): ?>
                                                                <a class="dropdown-item" href="<?= base_url('exercise/activate/' . $exercise['id']) ?>">
                                                                    <i class="fas fa-play mr-2"></i>Activate
                                                                </a>
                                                            <?php endif; ?>
                                                            <?php if ($exercise['status'] === 'active'): ?>
                                                                <a class="dropdown-item" href="<?= base_url('exercise/complete/' . $exercise['id']) ?>">
                                                                    <i class="fas fa-check mr-2"></i>Complete
                                                                </a>
                                                            <?php endif; ?>
                                                            <?php if ($exercise['status'] === 'completed'): ?>
                                                                <a class="dropdown-item" href="<?= base_url('exercise/archive/' . $exercise['id']) ?>">
                                                                    <i class="fas fa-archive mr-2"></i>Archive
                                                                </a>
                                                            <?php endif; ?>
                                                            <div class="dropdown-divider"></div>
                                                            <?php if ($exercise['stats']['applicants_count'] == 0 && $exercise['stats']['positions_count'] == 0): ?>
                                                                <a class="dropdown-item text-danger" href="<?= base_url('exercise/delete/' . $exercise['id']) ?>"
                                                                   onclick="return confirm('Are you sure you want to delete this exercise?')">
                                                                    <i class="fas fa-trash mr-2"></i>Delete
                                                                </a>
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
<?= $this->endSection() ?>
