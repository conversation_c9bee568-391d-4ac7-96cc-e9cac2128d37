<?php

namespace App\Controllers;

use App\Models\ApplicantModel;
use App\Models\PositionModel;
use App\Models\PositionsGroupModel;
use CodeIgniter\Controller;

class Applicants extends BaseController
{
    protected $applicantModel;
    protected $positionModel;
    protected $positionGroupModel;
    protected $authorize;

    public function __construct()
    {
        helper(['form', 'url', 'info', 'exercise']);
        $this->applicantModel = new ApplicantModel();
        $this->positionModel = new PositionModel();
        $this->positionGroupModel = new PositionsGroupModel();
        $this->authorize = service('authorize');
    }

    public function positionGroups()
    {
        // Ensure exercise context exists
        require_exercise_context();

        $exerciseId = get_current_exercise_id();
        $orgId = session('org_id');

        $groups = $this->positionGroupModel
            ->where('exercise_id', $exerciseId)
            ->where('org_id', $orgId)
            ->orderBy('group_name', 'ASC')
            ->findAll();

        $applicants = $this->applicantModel
            ->where('exercise_id', $exerciseId)
            ->where('org_id', $orgId)
            ->orderBy('name', 'ASC')
            ->findAll();

        //get position_id from applicants list positions without applicants
        $positions = [];
        $position_ids = array_column($applicants, 'position_id');
        if ($position_ids) {
            $positions = $this->positionModel
                ->where('exercise_id', $exerciseId)
                ->whereNotIn('id', $position_ids)
                ->orderBy('designation', 'ASC')
                ->findAll();
        }

        $data = [
            'title' => 'Position Groups',
            'menu' => "applicants",
            'groups' => $groups,
            'applicants' => $applicants,
            'positions' => $positions,
            'current_exercise' => get_current_exercise()
        ];

        return view('applicants/applicants_position_groups', $data);
    }

    public function index($groupId)
    {
        $group = $this->positionGroupModel->find($groupId);

        if (!$group) {
            return redirect()->to(site_url('applicants/positionGroups'))->with('error', 'Position group not found.');
        }

        $positions = $this->positionModel->where('position_group_id', $groupId)->findAll();

        // Calculate applicant count for each position
        foreach ($positions as &$position) {
            $position['applicant_count'] = $this->applicantModel->where('position_id', $position['id'])->countAllResults();
        }

        $data = [
            'title' => 'Positions in ' . $group['group_name'],
            'menu' => "applicants",
            'group' => $group,
            'positions' => $positions
        ];

        return view('applicants/applicants_index', $data);
    }

    public function viewPositions($group_id)
    {
        // Ensure exercise context exists
        require_exercise_context();

        $exerciseId = get_current_exercise_id();
        $orgId = session('org_id');

        $group = $this->positionGroupModel
            ->where('exercise_id', $exerciseId)
            ->where('org_id', $orgId)
            ->find($group_id);

        if (!$group) {
            return redirect()->to(site_url('applicants/positionGroups'))->with('error', 'Position group not found.');
        }

        $positions = $this->positionModel
            ->where('position_group_id', $group_id)
            ->where('exercise_id', $exerciseId)
            ->where('org_id', $orgId)
            ->findAll();

        // Calculate applicant count for each position
        foreach ($positions as &$position) {
            $position['applicant_count'] = $this->applicantModel
                ->where('position_id', $position['id'])
                ->where('exercise_id', $exerciseId)
                ->countAllResults();
        }

        $data = [
            'title' => 'Positions in ' . $group['group_name'],
            'menu' => "applicants",
            'group' => $group,
            'positions' => $positions,
            'current_exercise' => get_current_exercise()
        ];

        return view('applicants/applicants_index', $data);
    }

    public function applicants()
    {
        $positions = $this->positionModel->where('org_id', session('org_id'))
            ->orderBy('designation', 'ASC')
            ->findAll();

        $data = [
            'title' => 'Applicants',
            'menu' => "applicants",
            'positions' => $positions,
            'org_id' => session('org_id')
        ];

        return view('applicants/applicants_index', $data);
    }

    public function listByPosition($position_id)
    {
        // Ensure exercise context exists
        require_exercise_context();

        $exerciseId = get_current_exercise_id();
        $orgId = session('org_id');

        // Get position with join to position group to get group name
        $position = $this->positionModel
            ->select('positions.*, positions_groups.group_name')
            ->join('positions_groups', 'positions_groups.id = positions.position_group_id')
            ->where('positions.exercise_id', $exerciseId)
            ->where('positions.org_id', $orgId)
            ->find($position_id);

        if (!$position) {
            return redirect()->to(site_url('applicants'))->with('error', 'Position not found.');
        }

        $applicants = $this->applicantModel
            ->where('position_id', $position_id)
            ->where('exercise_id', $exerciseId)
            ->where('org_id', $orgId)
            ->orderBy('name', 'ASC')
            ->findAll();

        $data = [
            'title' => 'Applicants for ' . $position['designation'],
            'menu' => "applicants",
            'position' => $position,
            'applicants' => $applicants,
            'current_exercise' => get_current_exercise()
        ];

        return view('applicants/applicants_list', $data);
    }

    public function create($position_id)
    {
        // Ensure exercise context exists
        require_exercise_context();

        $exerciseId = get_current_exercise_id();
        $orgId = session('org_id');

        $position = $this->positionModel
            ->where('exercise_id', $exerciseId)
            ->where('org_id', $orgId)
            ->find($position_id);

        if (!$position) {
            return redirect()->to(site_url('applicants'))->with('error', 'Position not found.');
        }

        $data = [
            'title' => 'Add Applicant',
            'menu' => "applicants",
            'position_id' => $position_id,
            'org_id' => $position['org_id'],
            'position_group_id' => $position['position_group_id'],
            'current_exercise' => get_current_exercise()
        ];

        return view('applicants/applicants_create', $data);
    }

    public function store($position_id)
    {
        // Ensure exercise context exists
        require_exercise_context();

        $exerciseId = get_current_exercise_id();
        $orgId = session('org_id');

        $position = $this->positionModel
            ->where('exercise_id', $exerciseId)
            ->where('org_id', $orgId)
            ->find($position_id);

        if (!$position) {
            return redirect()->to(site_url('applicants'))->with('error', 'Position not found.');
        }

        $rules = [
            'name' => 'required|min_length[3]|max_length[255]',
        ];

        if ($this->validate($rules)) {
            $data = [
                'exercise_id' => $exerciseId,
                'position_id' => $position_id,
                'org_id' => $position['org_id'],
                'position_group_id' => $position['position_group_id'],
                'name' => $this->request->getVar('name'),
                'sex' => $this->request->getVar('sex'),
                'age' => $this->request->getVar('age'),
                'place_origin' => $this->request->getVar('place_origin'),
                'current_employer' => $this->request->getVar('current_employer'),
                'current_position' => $this->request->getVar('current_position'),
                'address_location' => $this->request->getVar('address_location'),
                'contact_details' => $this->request->getVar('contact_details'),
                'nid_number' => $this->request->getVar('nid_number'),
                'qualification_text' => $this->request->getVar('qualification_text'),
                'other_trainings' => $this->request->getVar('other_trainings'),
                'knowledge' => $this->request->getVar('knowledge'),
                'skills_competencies' => $this->request->getVar('skills_competencies'),
                'job_experiences' => $this->request->getVar('job_experiences'),
                'publications' => $this->request->getVar('publications'),
                'awards' => $this->request->getVar('awards'),
                'referees' => $this->request->getVar('referees'),
                'comments' => $this->request->getVar('comments'),
                'is_active' => $this->request->getVar('is_active') ?? "0",
            ];

            $this->applicantModel->insert($data);
            return redirect()->to(site_url("applicants/list/$position_id"))->with('success', 'Applicant added successfully.');
        } else {
            $data = [
                'title' => 'Add Applicant',
                'menu' => "applicants",
                'position_id' => $position_id,
                'org_id' => $position['org_id'],
                'position_group_id' => $position['position_group_id'],
                'current_exercise' => get_current_exercise(),
                'validation' => $this->validator
            ];
            return view('applicants/applicants_create', $data);
        }
    }

    public function edit($id)
    {
        $applicant = $this->applicantModel->find($id);

        if (empty($applicant)) {
            return redirect()->to(site_url('applicants'))->with('error', 'Applicant not found.');
        }

        $position = $this->positionModel->find($applicant['position_id']);

        $data = [
            'title' => 'Edit Applicant',
            'menu' => "applicants",
            'applicant' => $applicant,
            'position' => $position
        ];

        return view('applicants/applicants_edit', $data);
    }

    public function update($id)
    {
        $applicant = $this->applicantModel->find($id);

        if (empty($applicant)) {
            return redirect()->to(site_url('applicants'))->with('error', 'Applicant not found.');
        }

        $rules = [
            'name' => 'required|min_length[3]|max_length[255]',
        ];

        if ($this->validate($rules)) {
            $data = [
                'name' => $this->request->getVar('name'),
                'sex' => $this->request->getVar('sex'),
                'age' => $this->request->getVar('age'),
                'place_origin' => $this->request->getVar('place_origin'),
                'current_employer' => $this->request->getVar('current_employer'),
                'current_position' => $this->request->getVar('current_position'),
                'address_location' => $this->request->getVar('address_location'),
                'contact_details' => $this->request->getVar('contact_details'),
                'nid_number' => $this->request->getVar('nid_number'),
                'qualification_text' => $this->request->getVar('qualification_text'),
                'other_trainings' => $this->request->getVar('other_trainings'),
                'knowledge' => $this->request->getVar('knowledge'),
                'skills_competencies' => $this->request->getVar('skills_competencies'),
                'job_experiences' => $this->request->getVar('job_experiences'),
                'publications' => $this->request->getVar('publications'),
                'awards' => $this->request->getVar('awards'),
                'referees' => $this->request->getVar('referees'),
                'comments' => $this->request->getVar('comments'),
                'is_active' => $this->request->getVar('is_active') ?? "0",
            ];

            $this->applicantModel->update($id, $data);
            return redirect()->to(site_url("applicants/list/{$applicant['position_id']}"))->with('success', 'Applicant updated successfully.');
        } else {
            $position = $this->positionModel->find($applicant['position_id']);
            $data = [
                'title' => 'Edit Applicant',
                'menu' => "applicants",
                'applicant' => $applicant,
                'position' => $position,
                'validation' => $this->validator
            ];
            return view('applicants/applicants_edit', $data);
        }
    }

    public function delete($id)
    {
        $applicant = $this->applicantModel->find($id);

        if (empty($applicant)) {
            return redirect()->to(site_url('applicants'))->with('error', 'Applicant not found.');
        }

        $this->applicantModel->delete($id);

        return redirect()->to(site_url('applicants/list/' . $applicant['position_id']))->with('success', 'Applicant deleted successfully.');
    }

    public function import($position_id)
    {
        // Ensure exercise context exists
        require_exercise_context();

        $exerciseId = get_current_exercise_id();
        $orgId = session('org_id');

        $position = $this->positionModel
            ->where('exercise_id', $exerciseId)
            ->where('org_id', $orgId)
            ->find($position_id);

        if (!$position) {
            return redirect()->to(site_url('applicants'))->with('error', 'Position not found.');
        }

        $data = [
            'title' => 'Import Applicants',
            'menu' => "applicants",
            'position' => $position,
            'current_exercise' => get_current_exercise()
        ];

        return view('applicants/applicants_import', $data);
    }

    public function processImport($position_id)
    {
        // Ensure exercise context exists
        require_exercise_context();

        $exerciseId = get_current_exercise_id();
        $orgId = session('org_id');

        $position = $this->positionModel
            ->where('exercise_id', $exerciseId)
            ->where('org_id', $orgId)
            ->find($position_id);

        if (!$position) {
            return redirect()->to(site_url('applicants'))->with('error', 'Position not found.');
        }

        $file = $this->request->getFile('csv_file');

        if ($file->isValid() && !$file->hasMoved() && $file->getExtension() == 'csv') {
            $newName = $file->getRandomName();
            $file->move(WRITEPATH . 'uploads', $newName);

            $csvData = array_map('str_getcsv', file(WRITEPATH . 'uploads/' . $newName));
            array_shift($csvData); // Remove header row

            $importedCount = 0;
            $updatedCount = 0;

            foreach ($csvData as $row) {
                // Replace semicolons with line breaks in relevant fields
                $fieldsToReplace = [9, 10, 11, 12, 13, 14, 15, 16, 17];
                foreach ($fieldsToReplace as $index) {
                    if (!empty($row[$index])) {
                        $row[$index] = str_replace(';', "\n-", $row[$index]);
                    }
                }

                // Prepare applicant data
                $applicantData = [
                    'exercise_id' => $exerciseId,
                    'position_id' => $position_id,
                    'org_id' => $position['org_id'],
                    'position_group_id' => $position['position_group_id'],
                    'name' => $row[0],
                    'sex' => $row[1],
                    'age' => $row[2],
                    'place_origin' => $row[3],
                    'contact_details' => $row[4],
                    'nid_number' => $row[5],
                    'current_employer' => $row[6],
                    'current_position' => $row[7],
                    'address_location' => $row[8],
                    'qualification_text' => $row[9],
                    'other_trainings' => $row[10],
                    'knowledge' => $row[11],
                    'skills_competencies' => $row[12],
                    'job_experiences' => $row[13],
                    'publications' => $row[14],
                    'awards' => $row[15],
                    'referees' => $row[16],
                    'comments' => $row[17],
                    'is_active' => 1,
                ];

                // Check if an applicant with matching criteria already exists
                $existingApplicant = $this->applicantModel->where([
                    'name' => $row[0],
                    'sex' => $row[1],
                    'age' => $row[2],
                    'position_id' => $position_id,
                    'exercise_id' => $exerciseId
                ])->first();

                if ($existingApplicant) {
                    // Update existing applicant
                    if ($this->applicantModel->update($existingApplicant['id'], $applicantData)) {
                        $updatedCount++;
                    }
                } else {
                    // Insert new applicant
                    if ($this->applicantModel->insert($applicantData)) {
                        $importedCount++;
                    }
                }
            }

            $message = "Import completed. New records: $importedCount, Updated records: $updatedCount";
            return redirect()->to(site_url("applicants/list/$position_id"))->with('success', $message);
        } else {
            return redirect()->to(site_url("applicants/import/$position_id"))->with('error', 'Invalid file. Please upload a CSV file.');
        }
    }

    public function applicant_import($position_id)
    {
        // Ensure exercise context exists
        require_exercise_context();

        $exerciseId = get_current_exercise_id();
        $orgId = session('org_id');

        $position = $this->positionModel
            ->where('exercise_id', $exerciseId)
            ->where('org_id', $orgId)
            ->find($position_id);

        if (!$position) {
            return redirect()->to(site_url('applicants'))->with('error', 'Position not found.');
        }

        $data = [
            'title' => 'Import Existing Applicant',
            'menu' => "applicants",
            'position' => $position,
            'current_exercise' => get_current_exercise()
        ];

        return view('applicants/applicants_applicant_import', $data);
    }



    public function processApplicantImport($position_id)
    {
        // Ensure exercise context exists
        require_exercise_context();

        $exerciseId = get_current_exercise_id();
        $orgId = session('org_id');

        $position = $this->positionModel
            ->where('exercise_id', $exerciseId)
            ->where('org_id', $orgId)
            ->find($position_id);

        if (!$position) {
            return redirect()->to(site_url('applicants'))->with('error', 'Position not found.');
        }

        $applicant_id = $this->request->getPost('applicant_id');
        $sourceApplicant = $this->applicantModel->find($applicant_id);

        if ($sourceApplicant) {
            // Prepare applicant data
            $applicantData = [
                'exercise_id' => $exerciseId,
                'position_id' => $position_id,
                'org_id' => $position['org_id'],
                'position_group_id' => $position['position_group_id'],
                'name' => $sourceApplicant['name'],
                'sex' => $sourceApplicant['sex'],
                'age' => $sourceApplicant['age'],
                'place_origin' => $sourceApplicant['place_origin'],
                'contact_details' => $sourceApplicant['contact_details'],
                'nid_number' => $sourceApplicant['nid_number'],
                'current_employer' => $sourceApplicant['current_employer'],
                'current_position' => $sourceApplicant['current_position'],
                'address_location' => $sourceApplicant['address_location'],
                'qualification_text' => $sourceApplicant['qualification_text'],
                'other_trainings' => $sourceApplicant['other_trainings'],
                'knowledge' => $sourceApplicant['knowledge'],
                'skills_competencies' => $sourceApplicant['skills_competencies'],
                'job_experiences' => $sourceApplicant['job_experiences'],
                'publications' => $sourceApplicant['publications'],
                'awards' => $sourceApplicant['awards'],
                'referees' => $sourceApplicant['referees'],
                'comments' => $sourceApplicant['comments'],
                'is_active' => 1
            ];

            // Check if applicant already exists in this position
            $existingApplicant = $this->applicantModel->where([
                'name' => $sourceApplicant['name'],
                'position_id' => $position_id,
                'exercise_id' => $exerciseId
            ])->first();

            if ($existingApplicant) {
                // Update existing applicant
                if ($this->applicantModel->update($existingApplicant['id'], $applicantData)) {
                    return redirect()->to(site_url("applicants/list/$position_id"))
                        ->with('success', 'Applicant details updated successfully.');
                } else {
                    return redirect()->to(site_url("applicants/list/$position_id"))
                        ->with('error', 'Failed to update applicant details.');
                }
            } else {
                // Insert new applicant
                if ($this->applicantModel->insert($applicantData)) {
                    return redirect()->to(site_url("applicants/list/$position_id"))
                        ->with('success', 'Applicant imported successfully.');
                } else {
                    return redirect()->to(site_url("applicants/list/$position_id"))
                        ->with('error', 'Failed to import applicant.');
                }
            }
        }
    }

    public function shortlist($id)
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(400)->setJSON(['error' => 'Invalid request method']);
        }

        $applicant = $this->applicantModel->find($id);
        if (!$applicant) {
            return $this->response->setStatusCode(404)->setJSON(['error' => 'Applicant not found']);
        }

        $reason = $this->request->getPost('shortlistReason');
        if (empty($reason)) {
            return $this->response->setStatusCode(400)->setJSON(['error' => 'Reason is required']);
        }

        $data = [
            'application_status' => 'Shortlisted',
            'app_status_reason' => $reason,
            'updated_by' => session('user_id')
        ];

        if ($this->applicantModel->update($id, $data)) {
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Applicant has been shortlisted successfully'
            ]);
        }

        return $this->response->setStatusCode(500)->setJSON([
            'error' => 'Failed to shortlist applicant'
        ]);
    }

    public function eliminate($id)
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(400)->setJSON(['error' => 'Invalid request method']);
        }

        $applicant = $this->applicantModel->find($id);
        if (!$applicant) {
            return $this->response->setStatusCode(404)->setJSON(['error' => 'Applicant not found']);
        }

        $reason = $this->request->getPost('eliminateReason');
        if (empty($reason)) {
            return $this->response->setStatusCode(400)->setJSON(['error' => 'Reason is required']);
        }

        $data = [
            'application_status' => 'Eliminated',
            'app_status_reason' => $reason,
            'updated_by' => session('user_id')
        ];

        if ($this->applicantModel->update($id, $data)) {
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Applicant has been eliminated successfully'
            ]);
        }

        return $this->response->setStatusCode(500)->setJSON([
            'error' => 'Failed to eliminate applicant'
        ]);
    }

    public function withdraw($id)
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(400)->setJSON(['error' => 'Invalid request method']);
        }

        $applicant = $this->applicantModel->find($id);
        if (!$applicant) {
            return $this->response->setStatusCode(404)->setJSON(['error' => 'Applicant not found']);
        }

        $reason = $this->request->getPost('withdrawReason');
        if (empty($reason)) {
            return $this->response->setStatusCode(400)->setJSON(['error' => 'Reason is required']);
        }

        $data = [
            'application_status' => 'Withdrawn',
            'app_status_reason' => $reason,
            'updated_by' => session('user_id')
        ];

        if ($this->applicantModel->update($id, $data)) {
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Applicant has been withdrawn successfully'
            ]);
        }

        return $this->response->setStatusCode(500)->setJSON([
            'error' => 'Failed to withdraw applicant'
        ]);
    }

    public function search()
    {
        $query = $this->request->getGet('query');
        $exerciseId = get_current_exercise_id();
        $model = new ApplicantModel();

        $results = $model->select('applicants.id, applicants.*, positions.*, positions_groups.*')
            ->join('positions', 'positions.id = applicants.position_id')
            ->join('positions_groups', 'positions_groups.id = positions.position_group_id')
            ->where('applicants.exercise_id', $exerciseId)
            ->where('applicants.org_id', session('org_id'))
            ->like('applicants.name', $query)
            ->orderBy('applicants.name', 'ASC')
            ->findAll();

        return $this->response->setJSON($results);
    }

    public function searchApplicants()
    {
        $term = $this->request->getGet('term');
        $position_id = $this->request->getGet('position_id');

        // Get current position details
        $position = $this->positionModel->find($position_id);

        $applicants = $this->applicantModel
            ->select('applicants.*, positions.designation as position_name')
            ->join('positions', 'positions.id = applicants.position_id')
            ->where('applicants.exercise_id', get_current_exercise_id())
            ->where('applicants.org_id', session('org_id'))
            ->where('applicants.position_id !=', $position_id) // Exclude current position
            ->like('applicants.name', $term)
            ->findAll();

        $formattedResults = array_map(function($applicant) {
            return [
                'id' => $applicant['id'],
                'text' => $applicant['name'] . ' (' . $applicant['position_name'] . ')',
                'name' => $applicant['name'],
                'current_position' => $applicant['position_name']
            ];
        }, $applicants);

        return $this->response->setJSON(['results' => $formattedResults]);
    }

    public function generateTotal($groupId)
    {
        $applicantModel = new ApplicantModel();

        // Get all applicants for the given group
        $applicants = $applicantModel->where('position_group_id', $groupId)->findAll();

        // Update the totals in the applicants database
        foreach ($applicants as $applicant) {
            $rate_total = $applicant['rate_age'] +
                $applicant['rate_qualification'] +
                $applicant['rate_capability'] +
                $applicant['rate_trainings'] +
                $applicant['rate_skills_competencies'] +
                $applicant['rate_knowledge'] +
                $applicant['rate_public_service'] +
                $applicant['rate_private_non_relevant'] +
                $applicant['rate_private_relevant'] +
                $applicant['rate_public_non_relevant'] +
                $applicant['rate_public_relevant'];

            $max_rate_total = $applicant['max_rate_age'] +
                $applicant['max_rate_qualification'] +
                $applicant['max_rate_capability'] +
                $applicant['max_rate_trainings'] +
                $applicant['max_rate_skills_competencies'] +
                $applicant['max_rate_knowledge'] +
                $applicant['max_rate_public_service'] +
                $applicant['max_rate_private_non_relevant'] +
                $applicant['max_rate_private_relevant'] +
                $applicant['max_rate_public_non_relevant'] +
                $applicant['max_rate_public_relevant'];

            $applicantModel->update($applicant['id'], [
                'rate_total' => $rate_total,
                'max_rate_total' => $max_rate_total
            ]);
        }

        // Redirect to the position group view
        return redirect()->back()->with('success', 'Total generated successfully.');
    }

    public function applicantProfiles($position_id)
    {
        $position = $this->positionModel->find($position_id);
        if (!$position) {
            return redirect()->to(site_url('applicants'))->with('error', 'Position not found.');
        }

        $applicants = $this->applicantModel->where('position_id', $position_id)->findAll();

        $data = [
            'title' => 'Applicant Profiles for ' . $position['designation'],
            'menu' => "reports",
            'position' => $position,
            'applicants' => $applicants
        ];

        return view('reports/applicant_profiles', $data);
    }
}
